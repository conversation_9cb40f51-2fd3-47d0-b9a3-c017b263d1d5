# 小车行驶平滑性优化指南

## 问题分析

基于代码分析和PID调优数据，车轮抖动问题的主要原因：

### 1. 控制系统层面
- **控制频率偏低**: 当前5ms任务周期，PID控制频率200Hz
- **速度反馈噪声**: 虽然有滤波(α=0.3)，但可能不够平滑
- **死区补偿缺失**: 代码中死区补偿被注释掉了
- **PWM输出跳变**: 直接输出PID结果，缺乏平滑处理

### 2. 机械系统层面
- **电机死区**: 低速时电机响应不线性
- **机械间隙**: 减速器和传动系统的间隙
- **负载不均**: 左右轮负载或摩擦不一致

## 立即可实施的解决方案

### 方案一：优化速度滤波（推荐优先尝试）

**修改文件**: `24_h_ok_bno08x\User\Module\encoder\encoder_driver.h`

```c
// 将滤波系数从0.3改为0.1，增强滤波效果
#define ENCODER_FILTER_ALPHA 0.1f  // 原值0.3f，改为0.1f
```

**效果**: 速度反馈更平滑，减少高频噪声引起的抖动

### 方案二：启用死区补偿

**修改文件**: `24_h_ok_bno08x\User\App\pid_app.c`

```c
// 在PID_Task()函数中，取消死区补偿的注释
// 将第208-210行改为：
if(abs(output_left) < MIN_PWM) output_left = 0;
if(abs(output_right) < MIN_PWM) output_right = 0;

// 同时调整MIN_PWM值
uint8_t MIN_PWM = 5;  // 从2改为5
```

**效果**: 消除电机死区，避免低速时的不稳定

### 方案三：添加PWM输出平滑

**在PID_Task()函数中添加PWM平滑算法**:

```c
// 在duty_l和duty_r计算后添加平滑处理
static float last_duty_l = 0, last_duty_r = 0;
float smooth_factor = 0.7f;  // 平滑系数

// PWM输出平滑
duty_l = smooth_factor * duty_l + (1.0f - smooth_factor) * last_duty_l;
duty_r = smooth_factor * duty_r + (1.0f - smooth_factor) * last_duty_r;

last_duty_l = duty_l;
last_duty_r = duty_r;
```

**效果**: PWM输出变化更平缓，减少电机突变

### 方案四：提高控制频率

**修改文件**: `24_h_ok_bno08x\User\my_scheduler.c`

```c
// 将PID任务频率从5ms改为2ms
{PID_Task, 2, 0},        // 原值5改为2
{Encoder_Task, 2, 0},    // 原值5改为2，保持同步
```

**效果**: 更快的控制响应，减少控制延迟

## 进阶解决方案

### 方案五：二阶滤波器

**替换一阶滤波为二阶低通滤波**:

```c
// 在encoder结构体中添加二阶滤波变量
typedef struct {
    // ... 现有成员
    float filter_x1, filter_x2;  // 滤波器状态变量
    float filter_y1, filter_y2;  // 滤波器输出历史
} Encoder;

// 二阶低通滤波实现（截止频率约10Hz）
float second_order_filter(float input, Encoder* enc) {
    // 二阶巴特沃斯滤波器系数（采样频率200Hz，截止频率10Hz）
    float a0 = 1.0f, a1 = -1.561f, a2 = 0.641f;
    float b0 = 0.020f, b1 = 0.040f, b2 = 0.020f;
    
    float output = b0*input + b1*enc->filter_x1 + b2*enc->filter_x2
                 - a1*enc->filter_y1 - a2*enc->filter_y2;
    
    // 更新状态
    enc->filter_x2 = enc->filter_x1;
    enc->filter_x1 = input;
    enc->filter_y2 = enc->filter_y1;
    enc->filter_y1 = output;
    
    return output;
}
```

### 方案六：自适应死区补偿

```c
// 动态死区补偿
float adaptive_deadzone_compensation(float pid_output, float current_speed) {
    float deadzone = 3.0f;  // 基础死区
    
    // 根据当前速度调整死区
    if(abs(current_speed) < 2.0f) {
        deadzone = 8.0f;  // 低速时增大死区补偿
    }
    
    if(abs(pid_output) > 0.1f && abs(pid_output) < deadzone) {
        return (pid_output > 0) ? deadzone : -deadzone;
    }
    
    return pid_output;
}
```

## 硬件检查清单

### 机械检查
1. **轮子平衡**: 检查左右轮是否平衡，重量是否一致
2. **轴承润滑**: 检查轮轴是否顺滑转动
3. **地面接触**: 确保两轮同时接触地面，无悬空
4. **传动间隙**: 检查减速器和联轴器间隙

### 电气检查
1. **电源稳定性**: 测量电机供电是否稳定，有无纹波
2. **编码器信号**: 检查编码器信号质量，有无干扰
3. **PWM频率**: 确认PWM频率足够高（建议>1kHz）
4. **接地良好**: 检查系统接地是否良好

## 实施建议

### 第一步：快速改善（5分钟）
1. 修改滤波系数：`ENCODER_FILTER_ALPHA 0.3f → 0.1f`
2. 启用死区补偿：取消注释并调整`MIN_PWM = 5`

### 第二步：进一步优化（15分钟）
1. 添加PWM输出平滑算法
2. 提高控制频率到2ms

### 第三步：深度优化（30分钟）
1. 实施二阶滤波器
2. 添加自适应死区补偿

### 第四步：硬件检查（根据需要）
1. 机械系统检查
2. 电气系统测试

## 效果验证

### 验证指标
1. **主观感受**: 车轮运行是否更平滑
2. **速度稳定性**: ZN_data中速度波动是否减少
3. **响应性**: 系统响应是否仍然及时
4. **功耗**: 电机功耗是否合理

### 数据监控
```
// 监控关键指标
- 速度变异系数: 目标<10%
- PWM输出平滑度: 相邻采样点差值<5%
- 控制延迟: <10ms
- 系统稳定性: 无振荡
```

## 故障排除

### 如果改善不明显
1. 检查机械系统（轮子、轴承、传动）
2. 测量电源稳定性
3. 考虑更换更高精度的编码器
4. 检查PWM驱动电路

### 如果出现新问题
1. 系统不稳定：减小滤波强度，恢复原参数
2. 响应变慢：适当提高滤波截止频率
3. 功耗增加：检查死区补偿是否过大

---
**优化目标**: 实现平滑、稳定、响应及时的车轮控制  
**预期效果**: 显著减少抖动，提升行驶平滑性  
**实施难度**: 从简单到复杂，循序渐进
