#include "usart_app.h"
#include "jy901s_driver.h"

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

extern UART_HandleTypeDef huart5;

// 引入外部变量 - 用于CSV数据发送
extern Encoder left_encoder;
extern Encoder right_encoder;
extern PID_T pid_speed_left;
extern PID_T pid_speed_right;
extern PID_T pid_line; 
extern PID_T pid_angle;
extern float duty_l;
extern float duty_r;

// CSV数据发送控制标志 - 默认启用，上电即开始发送
bool csv_data_send_enable = true;

uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};
struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	// 初始化可变参数列表
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void uart_init(void)
{
	/* 串口环形缓冲区初始化，配置异步接收中断 */
  rt_ringbuffer_init(&uart_ringbuffer, ringbuffer_pool, sizeof(ringbuffer_pool));
	/* 启动串口DMA接收中断 */
	HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
	__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
}
	
/**
 * @brief UART DMA接收完成或空闲事件回调函数
 * @param huart UART句柄
 * @param Size 指示空闲事件发生前，DMA已经成功接收了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	// 1. 确认是目标串口 (USART1)
	if (huart->Instance == USART1)
	{
		// 2. 立即停止当前的 DMA 接收 (防止数据接收)
		//    因为空闲中断意味着发送方已经停止，禁止 DMA 继续等待数据
		HAL_UART_DMAStop(huart);

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);

		// 5. 清空 DMA 接收缓冲区为下次接收做准备
		//    虽然 memcpy 只复制了 Size 个数据，但清空整个缓冲区更安全
		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

		// 6. **关键步骤：重新启动下一轮 DMA 接收中断**
		//    如果不再次调用，串口只能接收一次
		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

		// 7. 因为之前关闭了半满中断，这里需要重新再次关闭 (非常重要)
		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
	}
}


/**
 * @brief 发送CSV格式数据 - ZN整定值相关数据（包括左右轮速度）
 * @retval None
 */
void send_csv_data(void)
{
    if(!csv_data_send_enable) return;

    // 系统启动延迟 - 防止发送未初始化的数据
    static uint32_t startup_delay = 0;
    if(startup_delay < 1000) { // 1秒延迟
        startup_delay += 5; // 每次调用增加5ms (根据任务调度周期)
        return;
    }

    // 发送频率控制 - 每100ms发送一次数据，避免数据过于频繁
    static uint32_t last_send_time = 0;
    uint32_t current_time = HAL_GetTick();
    if(current_time - last_send_time < 100) { // 100ms间隔
        return;
    }
    last_send_time = current_time;

    // 按照FireWater协议格式发送CSV数据
    // 数据包括：左轮速度，右轮速度，左轮PID-kp，左轮PID-ki，左轮PID-kd，右轮PID-kp，右轮PID-ki，右轮PID-kd
    my_printf(&huart1, "ZN_data:%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f\r\n",
              left_encoder.filtered_speed_cm_s,    // 左轮速度 (cm/s)
              right_encoder.filtered_speed_cm_s,   // 右轮速度 (cm/s)
              pid_speed_left.kp,                   // 左轮PID-kp参数
              pid_speed_left.ki,                   // 左轮PID-ki参数
              pid_speed_left.kd,                   // 左轮PID-kd参数
              pid_speed_right.kp,                  // 右轮PID-kp参数
              pid_speed_right.ki,                  // 右轮PID-ki参数
              pid_speed_right.kd);                 // 右轮PID-kd参数
}

/**
 * @brief 发送调试信息 - 用于验证串口通信正常
 * @retval None
 */
void send_debug_info(void)
{
    static uint32_t debug_counter = 0;
    static uint32_t last_debug_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每2秒发送一次调试信息
    if(current_time - last_debug_time >= 2000) {
        last_debug_time = current_time;
        debug_counter++;

        my_printf(&huart1, "DEBUG: System running, counter=%lu, time=%lu\r\n",
                  debug_counter, current_time);
    }
}

void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&uart_ringbuffer);

	// 无论是否有接收数据，都尝试发送CSV数据
	// 发送CSV格式的ZN整定值相关数据
	send_csv_data();

	// 发送调试信息，确保串口通信正常
	send_debug_info();

	// 如果没有接收到数据，直接返回
	if (length == 0)
		return;

	rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);

	int num = 2;  // 改为int类型匹配%d格式
	int pwm = 2;  // 改为int类型匹配%d格式
	sscanf((char*)uart_dma_buffer, "set_%d:%d", &num, &pwm);
	if(num == 0)
	{
//		motor_set_l(pwm);
		pid_set_target(&pid_speed_left, pwm);
		my_printf(&huart1, "set_l_ok:%d\r\n", pwm);
	}
	else if(num == 1)
	{
		motor_set_r(pwm);
		pid_set_target(&pid_speed_right, pwm);
		my_printf(&huart1, "set_r_ok:%d\r\n", pwm);
	}
	else
	{
		my_printf(&huart1, "uart1:%s\r\n", uart_dma_buffer);
		my_printf(&huart5, "uart1:%s\r\n", uart_dma_buffer);
	}

	// 清空缓冲区
	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
}
