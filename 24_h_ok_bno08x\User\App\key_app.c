#include "key_app.h"

extern UART_HandleTypeDef huart1;

extern uint8_t led_rgb[5];
extern bool pid_running;

uint8_t runing_flat = 0;
uint16_t runing_time = 0;

uint8_t test = 0;
extern uint8_t first_flat;

extern unsigned char system_mode;

extern uint8_t stop_flat;
extern float g_last_yaw;
extern int g_revolution_count;
extern bool g_is_yaw_initialized;

// CSV数据发送控制标志 - 按键1触发数据发送
extern bool csv_data_send_enable;

void key_task(void)
{
    static uint8_t key_old = 0;
    static uint8_t key_debounce_count[5] = {0}; // 消抖计数器，支持5个按键
    static uint8_t key_stable_state = 0;        // 稳定状态

    uint8_t key_val = key_read();

    // 改进的消抖算法
    for(int i = 1; i <= 4; i++) // 检查按键1-4
    {
        uint8_t current_bit = (key_val == i) ? 1 : 0;
        uint8_t stable_bit = (key_stable_state & (1 << (i-1))) ? 1 : 0;

        if(current_bit != stable_bit)
        {
            // 状态不一致，增加计数器
            key_debounce_count[i-1]++;
            if(key_debounce_count[i-1] >= 3) // 连续3次检测到相同状态才认为有效
            {
                if(current_bit)
                    key_stable_state |= (1 << (i-1));  // 设置位
                else
                    key_stable_state &= ~(1 << (i-1)); // 清除位
                key_debounce_count[i-1] = 0;
            }
        }
        else
        {
            // 状态一致，重置计数器
            key_debounce_count[i-1] = 0;
        }
    }

    // 检测按键按下边沿（从稳定状态）
    uint8_t key_down = 0;
    for(int i = 1; i <= 4; i++)
    {
        uint8_t current_stable = (key_stable_state & (1 << (i-1))) ? 1 : 0;
        uint8_t old_stable = (key_old & (1 << (i-1))) ? 1 : 0;

        if(current_stable && !old_stable)
        {
            key_down = i; // 检测到按键i的按下边沿
            break;
        }
    }

    key_old = key_stable_state;

//	if(runing_flat)
//	{
//		static uint16_t num = 0;
//		static uint8_t temp = 0;
//		if(++temp >= 10)
//		{
//			temp = 0;
//			num += 5;
//		}
//
//		pid_set_target(&pid_speed_left, num);
//    pid_set_target(&pid_speed_right, num);
//	}
//
//	if(runing_flat)
//	{
//		if(++runing_time >= 200)
//		{
//			runing_flat = 0;
//			runing_time = 0;
//			num = 0;
//			motor_set_l(0);
//			motor_set_r(0);
//			pid_running = 0;
//		}
//	}
//
//	if(uwTick%1000 <= 10)
//		my_printf(&huart1, "%d\r\n", uwTick);

    switch(key_down)
    {
    case 1://Key1 - 启动PID控制并开始发送CSV数据
        pid_running = 1;
        stop_flat = 0;
        csv_data_send_enable = true; // 开始发送CSV数据
        // 添加按键响应确认信息
        my_printf(&huart1, "KEY1_PRESSED: PID_STARTED, CSV_ENABLED\r\n");
//				pid_set_target(&pid_speed_left, 15);
//				pid_set_target(&pid_speed_right, 15);
        break;
    case 2://Key2
        led_rgb[0] = 1;
        led_rgb[1] = 0;
        led_rgb[2] = 0;

//			pid_set_target(&pid_speed_left, 90);
//			pid_set_target(&pid_speed_right, 90);

//			motor_set_l(100);1
//			motor_set_r(100);
//			runing_flat = 1;
//			pid_running = 1;
        break;
    case 3://Key3 -
        led_rgb[0] = 0;
        led_rgb[1] = 1;
        led_rgb[2] = 1;

        system_mode = (system_mode + 1)%5;

//			first_flat = 0;
//			g_last_yaw = 0.0f;
//			g_revolution_count = 0;
//			g_is_yaw_initialized = false;
//			pid_reset(&pid_line);
//			pid_reset(&pid_angle);
//			bno080_task();

        break;
    case 4://Key4 -



        break;
    case 10://User Key -



        break;
    }
}
