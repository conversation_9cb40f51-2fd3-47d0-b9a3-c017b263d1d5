# ZN整定值串口数据发送修复报告

## 问题描述
用户反馈系统只能在上电时发送一次ZN整定值数据，且数据显示为乱码。

## 问题分析

### 1. 任务调度问题
**原始代码问题**：
```c
// main.c 中的错误实现
for(uint8_t i=0; i<8; i++) {
    all_task[i].task_fun();
}
```
**问题**：任务被连续执行，没有按照设定的时间间隔运行，导致数据发送频率异常。

### 2. CSV数据发送控制问题
**原始代码问题**：
```c
bool csv_data_send_enable = false;  // 默认关闭
```
**问题**：需要按键1触发才能开始发送数据，不符合上电即发送的需求。

### 3. 串口波特率问题
**原始代码问题**：
```c
huart1.Init.BaudRate = 460800;  // 过高的波特率
```
**问题**：460800波特率可能导致数据传输不稳定，出现乱码。

### 4. 数据发送频率控制缺失
**原始代码问题**：没有对数据发送频率进行控制，可能导致数据发送过于频繁。

## 修复方案

### 1. 修复任务调度器
**修复后代码**：
```c
// main.c - 使用正确的任务调度
while (1) {
    all_task_run();  // 按时间间隔执行任务
}
```

### 2. 启用CSV数据发送
**修复后代码**：
```c
bool csv_data_send_enable = true;  // 上电即启用
```

### 3. 降低串口波特率
**修复后代码**：
```c
huart1.Init.BaudRate = 115200;  // 标准稳定波特率
```

### 4. 添加数据发送频率控制
**修复后代码**：
```c
void send_csv_data(void) {
    // 发送频率控制 - 每100ms发送一次
    static uint32_t last_send_time = 0;
    uint32_t current_time = HAL_GetTick();
    if(current_time - last_send_time < 100) {
        return;
    }
    last_send_time = current_time;
    
    // 发送ZN整定值数据
    my_printf(&huart1, "ZN_data:%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f\r\n", 
              left_encoder.filtered_speed_cm_s,
              right_encoder.filtered_speed_cm_s,
              pid_speed_left.kp, pid_speed_left.ki, pid_speed_left.kd,
              pid_speed_right.kp, pid_speed_right.ki, pid_speed_right.kd);
}
```

### 5. 添加调试功能
**新增功能**：
```c
void send_debug_info(void) {
    // 每2秒发送一次调试信息，验证串口通信正常
    static uint32_t debug_counter = 0;
    static uint32_t last_debug_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    if(current_time - last_debug_time >= 2000) {
        last_debug_time = current_time;
        debug_counter++;
        my_printf(&huart1, "DEBUG: System running, counter=%lu, time=%lu\r\n", 
                  debug_counter, current_time);
    }
}
```

## 数据格式说明

### ZN整定值数据格式
```
ZN_data:左轮速度,右轮速度,左轮Kp,左轮Ki,左轮Kd,右轮Kp,右轮Ki,右轮Kd
```

### 示例输出
```
ZN_data:15.50,16.20,2.50,0.10,0.05,2.60,0.12,0.06
DEBUG: System running, counter=1, time=2000
ZN_data:15.45,16.18,2.50,0.10,0.05,2.60,0.12,0.06
```

## 验证方法

1. **编译并烧录程序**
2. **连接串口调试工具**（波特率设置为115200）
3. **上电后观察输出**：
   - 应该看到每100ms输出一次ZN_data数据
   - 应该看到每2秒输出一次DEBUG调试信息
   - 数据应该是可读的，不再是乱码

## 预期效果

- ✅ 上电即开始发送ZN整定值数据
- ✅ 数据发送频率稳定（100ms间隔）
- ✅ 串口数据清晰可读，无乱码
- ✅ 包含调试信息，便于问题排查
- ✅ 系统运行稳定，任务调度正常

## 技术要点

1. **任务调度**：使用基于时间的任务调度器，确保各任务按预定间隔执行
2. **频率控制**：通过HAL_GetTick()实现精确的时间控制
3. **波特率选择**：115200是最稳定的标准波特率
4. **数据格式**：使用CSV格式便于数据解析和处理
5. **调试支持**：内置调试信息输出，便于问题定位

修复完成后，系统将能够稳定、连续地发送ZN整定值相关数据。
