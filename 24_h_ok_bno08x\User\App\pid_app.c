#include "pid_app.h"
#include <stdlib.h>  // 添加abs函数声明
#include "math.h"

#define V_L_MAX 60
#define V_R_MAX 60

extern UART_HandleTypeDef huart1;

extern Encoder left_encoder;
extern Encoder right_encoder;

int basic_speed = 80; // 基础速度

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环

PID_T pid_line;        // 循迹环

PID_T pid_angle;       // 角度环

/* PID 参数配置 */
//PidParams_t pid_params_left = {
//    .kp = 6.8f,     // 增强响应速度控制，加快收敛速度
//    .ki = 0.175f,     // 提高稳定速度，加快靠近目标
//    .kd = 0.5f,     // 适当微分增益末期过冲
//    .out_min = -V_L_MAX,
//    .out_max = V_L_MAX,
//};

//PidParams_t pid_params_right = {
//    .kp = 7.0f,     // 增强响应速度控制，加快收敛速度
//    .ki = 0.2f,     // 提高稳定速度，加快靠近目标
//    .kd = 0.4f,     // 适当微分增益末期过冲			
//    .out_min = -V_R_MAX,
//    .out_max = V_R_MAX,
//};
PidParams_t pid_params_left =
{
    .kp = 4.8f,     // 第一阶段优化后的稳定Kp值
    .ki = 0.15f,    // 第二阶段：引入积分控制消除稳态误差
    .kd = 0.08f,    // 第一阶段优化后的Kd值
    .out_min = -V_L_MAX,
    .out_max = V_L_MAX,

};
PidParams_t pid_params_right =
{
    .kp = 4.2f,     // 第一阶段优化后的稳定Kp值（更保守）
    .ki = 0.20f,    // 第二阶段：更强的积分控制（解决左右轮不平衡）
    .kd = 0.10f,    // 第一阶段优化后的Kd值
    .out_min = -V_R_MAX,
    .out_max = V_R_MAX,
};


PidParams_t pid_params_line =
{
    .kp = 8.25f,
    .ki = 0.0f,
    .kd = 0.0f,
    .out_min = -80.0f,
    .out_max = 80.0f,
};


PidParams_t pid_params_angle =
{
    .kp = 0.6f,
    .ki = 0,
    .kd = 0.02f,
    .out_min = -V_R_MAX,
    .out_max = V_R_MAX,
};




void PID_Init(void)
{
    pid_init(&pid_speed_left,
             pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
             0.0f, pid_params_left.out_max);

    pid_init(&pid_speed_right,
             pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
             0.0f, pid_params_right.out_max);


    pid_init(&pid_line,
             pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
             0.0f, pid_params_line.out_max);
//
    pid_init(&pid_angle,
             pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
             0.0f, pid_params_angle.out_max);


    pid_set_target(&pid_speed_left, basic_speed);
    pid_set_target(&pid_speed_right, basic_speed);
    pid_set_target(&pid_line, 0);
    pid_set_target(&pid_angle, 0);
}

bool pid_running = false; // PID 运行使能控制

unsigned char pid_control_mode = 0; // 0-角度环控制，1-循迹环控制

void Line_PID_control(void) // 循迹环控制
{
    int line_pid_output = 0;

    // 使用位置式 PID 计算器计算循迹环控制输出
    line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);

    // 输出限幅
    line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);

    // 输出值作为速度环的目标输入
    pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
    pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
//	pid_set_target(&pid_speed_left, basic_speed);
//  pid_set_target(&pid_speed_right, basic_speed);
}

void Angle_PID_control(void) // 角度环控制
{
    int angle_pid_output = 0;

    float Yaw = get_yaw();

    // 使用位置式 PID 计算器计算角度环控制输出
    angle_pid_output = pid_calculate_positional(&pid_angle, Yaw);

    // 输出限幅
    angle_pid_output = pid_constrain(angle_pid_output, pid_params_angle.out_min, pid_params_angle.out_max);

    // 输出值作为速度环的目标输入
    pid_set_target(&pid_speed_left, basic_speed - angle_pid_output);
    pid_set_target(&pid_speed_right, basic_speed + angle_pid_output);
}

uint8_t stop_flat = 0;
uint8_t angle_flat = 0;

float duty_l;
float duty_r;


uint8_t MIN_PWM = 5;  // 增加死区补偿阈值，减少低速抖动

void PID_Task(void)
{
    if(pid_running == false) return;

    float output_left = 0, output_right = 0;

    if(angle_flat == 0 && ((system_mode == 3)||(system_mode == 4)))
    {
        pid_set_target(&pid_angle, 10);
        angle_flat = 1;
    }

    if(pid_control_mode == 0) // 角度环控制
    {
//      if(distance > 20000) // 达到指定距离时减速
//        basic_speed = 75;
//      else
//        basic_speed = 130;
        basic_speed = 10;

        Angle_PID_control();
    }
    else // 循迹环控制
    {
        basic_speed = 30;
        Line_PID_control();
    }

//		static uint16_t num = 0;
//		static uint8_t temp = 0;
//		if(++temp >= 50)
//		{
//			temp = 0;
//			num += 20;
//		}
//
//		pid_set_target(&pid_speed_left, num);
//    pid_set_target(&pid_speed_right, num);

//		Line_PID_control();

    if(stop_flat == 1)
    {
        pid_set_target(&pid_speed_left, 0);
        pid_set_target(&pid_speed_right, 0);
    }

    // 使用位置式 PID 计算器计算速度环控制输出
		output_left = pid_calculate_positional(&pid_speed_left, left_encoder.filtered_speed_cm_s);
		output_right = pid_calculate_positional(&pid_speed_right, right_encoder.filtered_speed_cm_s);

    // 输出限幅
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);


    // 在PID输出后添加死区补偿，减少低速抖动
    if(abs(output_left) < MIN_PWM) output_left = 0;
    if(abs(output_right) < MIN_PWM) output_right = 0;


    // 设置电机速度
    duty_l = output_left  / V_L_MAX * 100.0f;
    duty_r = output_right / V_R_MAX * 100.0f;

    // PWM输出平滑处理，减少抖动
    static float last_duty_l = 0, last_duty_r = 0;
    float smooth_factor = 0.7f;  // 平滑系数，0.7表示70%新值+30%旧值

    duty_l = smooth_factor * duty_l + (1.0f - smooth_factor) * last_duty_l;
    duty_r = smooth_factor * duty_r + (1.0f - smooth_factor) * last_duty_r;

    last_duty_l = duty_l;
    last_duty_r = duty_r;

    motor_set_l(duty_l);
    motor_set_r(duty_r);
//    motor_set_l(10);
//    motor_set_r(10);
		
//		my_printf(&huart1, "%f,%f\r\n", pid_line.target, g_line_position_error);
//		my_printf(&huart1, "%f,%f,%f\r\n", pid_speed_left.target,right_encoder.speed_cm_s, left_encoder.speed_cm_s);
//		my_printf(&huart1, "%f,%f\r\n", pid_speed_right.target, right_encoder.speed_cm_s);
}

